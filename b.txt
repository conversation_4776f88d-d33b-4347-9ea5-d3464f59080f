AUGMENT AGENT TOOL DESCRIPTIONS
=====================================

As Augment Agent, I have access to a comprehensive set of tools for code development, analysis, and project management:

FILE OPERATIONS
---------------
- view: View files/directories with regex search capabilities
- save-file: Create new files (up to 300 lines)
- str-replace-editor: Edit existing files with precise string replacement
- remove-files: Safely delete files

CODE ANALYSIS & RETRIEVAL
-------------------------
- codebase-retrieval: Search codebase using advanced context engine
- git-commit-retrieval: Search git history for relevant changes
- diagnostics: Get IDE issues/errors for specific files
- view-range-untruncated: View specific line ranges from truncated content
- search-untruncated: Search within truncated content

PROCESS MANAGEMENT
------------------
- launch-process: Execute shell commands (waiting or background)
- read-process: Read output from running processes
- write-process: Send input to running processes
- kill-process: Terminate processes by terminal ID
- list-processes: View all active processes
- read-terminal: Read from active VSCode terminal

WEB & RESEARCH
--------------
- web-search: Search the web using Google Custom Search API
- web-fetch: Fetch and convert web pages to Markdown
- open-browser: Open URLs in default browser

TASK MANAGEMENT
---------------
- view_tasklist: View current task list structure
- add_tasks: Create new tasks or subtasks
- update_tasks: Modify task properties (state, name, description)
- reorganize_tasklist: Restructure entire task list

VISUALIZATION & DOCUMENTATION
-----------------------------
- render-mermaid: Create interactive Mermaid diagrams
- remember: Store long-term memories for future reference

PACKAGE MANAGEMENT PHILOSOPHY
-----------------------------
I always use appropriate package managers (npm, pip, cargo, etc.) rather than manually editing package files, ensuring proper dependency resolution and avoiding version conflicts.

CODING BEST PRACTICES
--------------------
- Gather context before making changes using codebase-retrieval
- Use task management for complex multi-step work
- Wrap code snippets in <augment_code_snippet> tags for visibility
- Suggest testing after code changes
- Focus on user requirements without overstepping

SPECIALIZED CAPABILITIES
-----------------------
- Real-time codebase indexing and context-aware code search
- Git history analysis for understanding past changes
- Multi-language support across different programming ecosystems
- Integration with development environments and terminals
- Structured project planning and progress tracking

These tools enable me to provide comprehensive assistance with software development, from initial planning through implementation, testing, and deployment.
